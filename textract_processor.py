import json
import time
import boto3
import os
import logging
from typing import <PERSON><PERSON>, Dict, Optional
from botocore.exceptions import ClientError


def process_file_with_textract(
    file_path: str,
    region: str = 'us-east-1',
    bucket_name: Optional[str] = None,
    temp_prefix: str = 'temp-textract',
    logger: Optional[logging.Logger] = None
) -> Tuple[Dict, str, str]:
    """
    Process a local file with AWS Textract and return the raw response, formatted text, and reconstructed layout.

    Args:
        file_path (str): Local path to the file to process
        region (str): AWS region for Textract service
        bucket_name (Optional[str]): S3 bucket name for temporary file upload
        temp_prefix (str): S3 prefix for temporary files
        logger (Optional[logging.Logger]): Logger instance

    Returns:
        Tuple[Dict, str, str]: (textract_response, formatted_text_with_coordinates, reconstructed_layout)

    Example formatted text output:
        === TEXT WITH COORDINATES ===
        text, x1, y1, x2, y2
        SmartWay, 0.4509, 0.0311, 0.5840, 0.0538
        TRANSPORTATION, 0.4522, 0.0514, 0.6103, 0.0654
        ...

    Example reconstructed layout output:
        === RECONSTRUCTED LAYOUT ===
        SmartWay                    Invoice #: 12345
        TRANSPORTATION              Date: 01/15/2024
        ...
    """
    if logger is None:
        logger = logging.getLogger(__name__)
    
    # Initialize AWS clients
    textract_client = boto3.client('textract', region_name=region)
    s3_client = boto3.client('s3', region_name=region)
    
    if bucket_name is None:
        raise ValueError("bucket_name is required for S3 upload")
    
    try:
        # Step 1: Upload file to S3
        file_name = os.path.basename(file_path)
        s3_key = f"{temp_prefix}/{file_name}"
        logger.info(f"Uploading {file_path} to s3://{bucket_name}/{s3_key}")
        
        s3_client.upload_file(file_path, bucket_name, s3_key)
        s3_uri = f"s3://{bucket_name}/{s3_key}"
        logger.info(f"Successfully uploaded to {s3_uri}")
        
        # Step 2: Invoke Textract
        textract_response = _invoke_textract(textract_client, s3_uri, logger)
        
        # Step 3: Get final results
        textract_results = _check_textract_job_status(textract_client, textract_response, logger)
        
        # Step 4: Convert to formatted text
        formatted_text = _convert_textract_to_structured_format(textract_results, logger)

        # Step 5: Generate reconstructed layout
        reconstructed_layout = _reconstruct_layout_from_coordinates(textract_results, logger)

        # Step 6: Clean up S3 file (optional)
        try:
            s3_client.delete_object(Bucket=bucket_name, Key=s3_key)
            logger.info(f"Cleaned up temporary S3 file: {s3_uri}")
        except Exception as cleanup_error:
            logger.warning(f"Failed to clean up S3 file: {cleanup_error}")

        return textract_results, formatted_text, reconstructed_layout
        
    except Exception as e:
        logger.error(f"Error processing file {file_path}: {e}")
        raise


def _invoke_textract(textract_client, s3_uri: str, logger) -> dict:
    """Invoke Textract to extract text from document."""
    try:
        # Parse S3 URI to get bucket and key
        s3_parts = s3_uri.replace('s3://', '').split('/', 1)
        bucket = s3_parts[0]
        key = s3_parts[1]
        
        logger.info(f"Starting Textract text extraction for {s3_uri}")
        
        # Always try synchronous detection first for faster processing
        try:
            logger.info("Attempting synchronous Textract text detection")
            response = textract_client.detect_document_text(
                Document={
                    'S3Object': {
                        'Bucket': bucket,
                        'Name': key
                    }
                }
            )
            logger.info("Synchronous Textract text detection successful")
            return {'sync_response': response, 'is_sync': True}
        except ClientError as sync_error:
            # Check if it's a document size error (common reason for sync failure)
            error_code = sync_error.response.get('Error', {}).get('Code', '')
            if error_code in ['InvalidParameterException', 'UnsupportedDocumentException']:
                logger.info(f"Document too large for sync processing ({error_code}), using async")
            else:
                logger.warning(f"Synchronous Textract failed: {sync_error}")
            logger.info("Falling back to asynchronous Textract")
        
        # Asynchronous text detection - only text, no tables or forms for speed
        response = textract_client.start_document_text_detection(
            DocumentLocation={
                'S3Object': {
                    'Bucket': bucket,
                    'Name': key
                }
            }
            # No FeatureTypes specified = text only (fastest and cheapest)
        )
        
        job_id = response['JobId']
        logger.info(f"Textract async text detection job started with ID: {job_id}")
        
        return {'JobId': job_id, 'is_sync': False}
    except ClientError as e:
        logger.error(f"Error starting Textract job: {e}")
        raise


def _check_textract_job_status(textract_client, textract_response: dict, logger) -> dict:
    """Check the status of the Textract job or return sync response."""
    try:
        # Handle synchronous response
        if textract_response.get('is_sync'):
            logger.info("Using synchronous Textract response")
            return textract_response['sync_response']
        
        # Handle asynchronous response
        job_id = textract_response.get('JobId')
        if not job_id:
            raise ValueError("No JobId found in Textract response")
        
        logger.info(f"Monitoring Textract job status for ID: {job_id}")
        while True:
            response = textract_client.get_document_text_detection(JobId=job_id)
            status = response.get('JobStatus')
            logger.info(f"Textract job status: {status}")
            
            if status in ['SUCCEEDED', 'FAILED']:
                logger.info(f"Textract job completed with status: {status}")
                return response
            elif status == 'IN_PROGRESS':
                # Reduced sleep time for faster polling
                time.sleep(5)
            else:
                raise ValueError(f"Unexpected job status: {status}")
    except ClientError as e:
        logger.error(f"Error checking Textract job status: {e}")
        raise


def _convert_textract_to_structured_format(textract_response: dict, logger) -> str:
    """Convert Textract response to structured text format for LLM processing."""
    try:
        blocks = textract_response.get('Blocks', [])

        # Extract text blocks with coordinates in the required format
        text_lines = []

        # Process LINE blocks for text with coordinates (most efficient)
        for block in blocks:
            if block['BlockType'] == 'LINE':
                bbox = block.get('Geometry', {}).get('BoundingBox', {})
                text = block.get('Text', '')

                # Properly escape text for CSV format - enclose in quotes if contains comma
                if ',' in text:
                    # Escape any existing quotes by doubling them, then wrap in quotes
                    escaped_text = text.replace('"', '""')
                    text = f'"{escaped_text}"'

                # Convert to x1, y1, x2, y2 format
                x1 = bbox.get('Left', 0)
                y1 = bbox.get('Top', 0)
                x2 = x1 + bbox.get('Width', 0)
                y2 = y1 + bbox.get('Height', 0)

                text_lines.append(f"{text}, {x1:.4f}, {y1:.4f}, {x2:.4f}, {y2:.4f}")

        # If no LINE blocks found (sync response), use WORD blocks
        if not text_lines:
            logger.info("No LINE blocks found, using WORD blocks for text extraction")
            for block in blocks:
                if block['BlockType'] == 'WORD':
                    bbox = block.get('Geometry', {}).get('BoundingBox', {})
                    text = block.get('Text', '')

                    # Properly escape text for CSV format - enclose in quotes if contains comma
                    if ',' in text:
                        # Escape any existing quotes by doubling them, then wrap in quotes
                        escaped_text = text.replace('"', '""')
                        text = f'"{escaped_text}"'

                    # Convert to x1, y1, x2, y2 format
                    x1 = bbox.get('Left', 0)
                    y1 = bbox.get('Top', 0)
                    x2 = x1 + bbox.get('Width', 0)
                    y2 = y1 + bbox.get('Height', 0)

                    text_lines.append(f"{text}, {x1:.4f}, {y1:.4f}, {x2:.4f}, {y2:.4f}")

        # Build the structured text format (text only, no tables for speed)
        structured_text_parts = []

        # Add header
        structured_text_parts.append("=== TEXT WITH COORDINATES ===")
        structured_text_parts.append("text, x1, y1, x2, y2")

        # Add text lines
        for line in text_lines:
            structured_text_parts.append(line)

        structured_text = "\n".join(structured_text_parts)

        logger.info(f"Extracted {len(text_lines)} text lines from document")
        return structured_text

    except Exception as e:
        logger.error(f"Error converting Textract response to structured format: {e}")
        raise


def _reconstruct_layout_from_coordinates(textract_response: dict, logger) -> str:
    """
    Reconstruct the document layout from bounding box coordinates to preserve column structure.

    This function spatially organizes text elements based on their coordinates to maintain
    the original document layout, helping AI models distinguish between different columns
    and sections without mixing up values.

    Args:
        textract_response (dict): Textract response containing blocks with coordinates
        logger: Logger instance

    Returns:
        str: Reconstructed layout text that preserves spatial relationships
    """
    try:
        blocks = textract_response.get('Blocks', [])

        # Extract text elements with their coordinates
        text_elements = []

        # Process LINE blocks first (preferred for layout reconstruction)
        for block in blocks:
            if block['BlockType'] == 'LINE':
                bbox = block.get('Geometry', {}).get('BoundingBox', {})
                text = block.get('Text', '').strip()

                if text:  # Only include non-empty text
                    x1 = bbox.get('Left', 0)
                    y1 = bbox.get('Top', 0)
                    x2 = x1 + bbox.get('Width', 0)
                    y2 = y1 + bbox.get('Height', 0)

                    text_elements.append({
                        'text': text,
                        'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2,
                        'center_x': (x1 + x2) / 2,
                        'center_y': (y1 + y2) / 2
                    })

        # If no LINE blocks found, use WORD blocks and group them into lines
        if not text_elements:
            logger.info("No LINE blocks found, reconstructing layout from WORD blocks")
            word_elements = []

            for block in blocks:
                if block['BlockType'] == 'WORD':
                    bbox = block.get('Geometry', {}).get('BoundingBox', {})
                    text = block.get('Text', '').strip()

                    if text:
                        x1 = bbox.get('Left', 0)
                        y1 = bbox.get('Top', 0)
                        x2 = x1 + bbox.get('Width', 0)
                        y2 = y1 + bbox.get('Height', 0)

                        word_elements.append({
                            'text': text,
                            'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2,
                            'center_x': (x1 + x2) / 2,
                            'center_y': (y1 + y2) / 2
                        })

            # Group words into lines based on vertical proximity
            text_elements = _group_words_into_lines(word_elements)

        if not text_elements:
            logger.warning("No text elements found for layout reconstruction")
            return "=== RECONSTRUCTED LAYOUT ===\n[No text content found]"

        # Sort elements by vertical position (top to bottom)
        text_elements.sort(key=lambda x: x['center_y'])

        # Group elements into rows based on vertical proximity
        rows = _group_elements_into_rows(text_elements)

        # Build the reconstructed layout
        layout_lines = ["=== RECONSTRUCTED LAYOUT ==="]

        for row in rows:
            # Sort elements in each row by horizontal position (left to right)
            row.sort(key=lambda x: x['center_x'])

            # Create a line with proper spacing to preserve column structure
            line = _create_spaced_line(row)
            layout_lines.append(line)

        reconstructed_layout = "\n".join(layout_lines)

        logger.info(f"Reconstructed layout with {len(rows)} rows from {len(text_elements)} text elements")
        return reconstructed_layout

    except Exception as e:
        logger.error(f"Error reconstructing layout from coordinates: {e}")
        return f"=== RECONSTRUCTED LAYOUT ===\n[Error reconstructing layout: {e}]"


def _group_words_into_lines(word_elements: list) -> list:
    """Group individual words into lines based on vertical proximity."""
    if not word_elements:
        return []

    # Sort words by vertical position
    word_elements.sort(key=lambda x: x['center_y'])

    lines = []
    current_line_words = [word_elements[0]]

    for word in word_elements[1:]:
        # Check if this word is on the same line as the previous words
        # Use a threshold based on the height of the current line
        current_line_avg_y = sum(w['center_y'] for w in current_line_words) / len(current_line_words)
        current_line_height = max(w['y2'] - w['y1'] for w in current_line_words)

        # If the word is within 0.5 * line_height of the current line, add it to the current line
        if abs(word['center_y'] - current_line_avg_y) <= current_line_height * 0.5:
            current_line_words.append(word)
        else:
            # Create a line element from current words
            if current_line_words:
                line_element = _merge_words_into_line(current_line_words)
                lines.append(line_element)
            current_line_words = [word]

    # Add the last line
    if current_line_words:
        line_element = _merge_words_into_line(current_line_words)
        lines.append(line_element)

    return lines


def _merge_words_into_line(words: list) -> dict:
    """Merge multiple words into a single line element."""
    if not words:
        return {}

    # Sort words by horizontal position
    words.sort(key=lambda x: x['center_x'])

    # Combine text with spaces
    text_parts = [word['text'] for word in words]
    combined_text = ' '.join(text_parts)

    # Calculate bounding box for the entire line
    x1 = min(word['x1'] for word in words)
    y1 = min(word['y1'] for word in words)
    x2 = max(word['x2'] for word in words)
    y2 = max(word['y2'] for word in words)

    return {
        'text': combined_text,
        'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2,
        'center_x': (x1 + x2) / 2,
        'center_y': (y1 + y2) / 2
    }


def _group_elements_into_rows(elements: list) -> list:
    """Group text elements into rows based on vertical proximity."""
    if not elements:
        return []

    rows = []
    current_row = [elements[0]]

    for element in elements[1:]:
        # Check if this element is on the same row as the previous elements
        current_row_avg_y = sum(e['center_y'] for e in current_row) / len(current_row)
        current_row_height = max(e['y2'] - e['y1'] for e in current_row)

        # If the element is within 0.7 * row_height of the current row, add it to the current row
        if abs(element['center_y'] - current_row_avg_y) <= current_row_height * 0.7:
            current_row.append(element)
        else:
            # Start a new row
            rows.append(current_row)
            current_row = [element]

    # Add the last row
    if current_row:
        rows.append(current_row)

    return rows


def _create_spaced_line(row_elements: list) -> str:
    """Create a properly spaced line from row elements to preserve column structure."""
    if not row_elements:
        return ""

    if len(row_elements) == 1:
        return row_elements[0]['text']

    # Calculate spacing between elements based on their coordinates
    line_parts = []

    for i, element in enumerate(row_elements):
        line_parts.append(element['text'])

        # Add spacing to the next element if not the last one
        if i < len(row_elements) - 1:
            current_x2 = element['x2']
            next_x1 = row_elements[i + 1]['x1']

            # Calculate the gap between elements
            gap = next_x1 - current_x2

            # Convert gap to approximate number of spaces
            # Assuming average character width is about 0.01 in normalized coordinates
            avg_char_width = 0.01
            num_spaces = max(1, int(gap / avg_char_width))

            # Limit the number of spaces to keep the output readable
            num_spaces = min(num_spaces, 20)

            line_parts.append(' ' * num_spaces)

    return ''.join(line_parts)


# Example usage
if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Example usage
    file_path = "data/input_data/10k_w_true_data/invoice_shortlisted_v2/SmartWay Transportation/processed/11509907_Invoice.pdf"
    bucket_name = "document-extraction-logistically"
    
    try:
        textract_response, formatted_text, reconstructed_layout = process_file_with_textract(
            file_path=file_path,
            bucket_name=bucket_name,
            logger=logger
        )

        print("=== TEXTRACT RESPONSE ===")
        print(json.dumps(textract_response, indent=2, default=str))

        print("\n=== FORMATTED TEXT WITH COORDINATES ===")
        print(formatted_text)

        print("\n=== RECONSTRUCTED LAYOUT ===")
        print(reconstructed_layout)
        
    except Exception as e:
        logger.error(f"Processing failed: {e}")
